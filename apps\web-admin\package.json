{"name": "web-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm exec next dev --turbopack --port 3002", "build": "npm run patch-styled-jsx && npx next build", "start": "pnpm exec next start --port 3002", "lint": "pnpm exec next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next", "patch-styled-jsx": "node scripts/patch-styled-jsx.js", "postinstall": "npm run patch-styled-jsx"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "firebase": "^10.13.1", "lucide-react": "^0.445.0", "next": "15.3.2", "react": "19.0.0", "react-dom": "19.0.0", "shared-types": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*", "firebase-config": "workspace:*", "api-client": "workspace:*", "business-logic": "workspace:*"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "15.3.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "typescript": "^5"}}