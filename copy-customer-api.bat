@echo off
echo Copying customer-specific API routes...

robocopy "apps\web\src\app\api\restaurants" "apps\web-customer\src\app\api\restaurants" /E
robocopy "apps\web\src\app\api\search" "apps\web-customer\src\app\api\search" /E
robocopy "apps\web\src\app\api\payments" "apps\web-customer\src\app\api\payments" /E
robocopy "apps\web\src\app\api\paymongo" "apps\web-customer\src\app\api\paymongo" /E
robocopy "apps\web\src\app\api\maps" "apps\web-customer\src\app\api\maps" /E
robocopy "apps\web\src\app\api\delivery" "apps\web-customer\src\app\api\delivery" /E
robocopy "apps\web\src\app\api\chatbot" "apps\web-customer\src\app\api\chatbot" /E
robocopy "apps\web\src\app\api\ai" "apps\web-customer\src\app\api\ai" /E
robocopy "apps\web\src\app\api\email" "apps\web-customer\src\app\api\email" /E
robocopy "apps\web\src\app\api\upload" "apps\web-customer\src\app\api\upload" /E
robocopy "apps\web\src\app\api\webhooks" "apps\web-customer\src\app\api\webhooks" /E

echo Customer API routes copied successfully!
