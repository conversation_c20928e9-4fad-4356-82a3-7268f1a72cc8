@echo off
echo Copying customer-specific pages...

robocopy "apps\web\src\app\cart" "apps\web-customer\src\app\cart" /E
robocopy "apps\web\src\app\orders" "apps\web-customer\src\app\orders" /E
robocopy "apps\web\src\app\profile" "apps\web-customer\src\app\profile" /E
robocopy "apps\web\src\app\search" "apps\web-customer\src\app\search" /E
robocopy "apps\web\src\app\wishlist" "apps\web-customer\src\app\wishlist" /E
robocopy "apps\web\src\app\vendors" "apps\web-customer\src\app\vendors" /E
robocopy "apps\web\src\app\payment-success" "apps\web-customer\src\app\payment-success" /E
robocopy "apps\web\src\app\payment-failed" "apps\web-customer\src\app\payment-failed" /E
robocopy "apps\web\src\app\ai-demo" "apps\web-customer\src\app\ai-demo" /E
robocopy "apps\web\src\app\analytics-demo" "apps\web-customer\src\app\analytics-demo" /E
robocopy "apps\web\src\app\redux-demo" "apps\web-customer\src\app\redux-demo" /E
robocopy "apps\web\src\app\paymongo-test" "apps\web-customer\src\app\paymongo-test" /E
robocopy "apps\web\src\app\system-docs" "apps\web-customer\src\app\system-docs" /E

echo Customer pages copied successfully!
