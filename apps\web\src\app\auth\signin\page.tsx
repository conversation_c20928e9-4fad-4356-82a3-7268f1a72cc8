'use client';

// Force dynamic rendering to avoid SSR issues
export const dynamic = 'force-dynamic';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { Store, Shield, Truck } from 'lucide-react';

export default function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [panelType, setPanelType] = useState<'customer' | 'vendor' | 'admin' | 'driver'>('customer');

  const { signIn, signInWithGoogle, user, loading: authLoading } = useAuth();
  const router = useRouter();

  // Detect panel type from hostname
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname;
      if (hostname.includes('tap2go-vendor') || hostname.startsWith('vendor.')) {
        setPanelType('vendor');
      } else if (hostname.includes('tap2go-admin') || hostname.startsWith('admin.')) {
        setPanelType('admin');
      } else if (hostname.includes('tap2go-driver') || hostname.startsWith('driver.')) {
        setPanelType('driver');
      } else {
        setPanelType('customer');
      }
    }
  }, []);

  // Redirect if already logged in based on role and panel validation
  React.useEffect(() => {
    if (!authLoading && user) {
      // Validate user role against panel type
      if (panelType === 'vendor' && user.role !== 'vendor' && user.role !== 'admin') {
        setError('This portal is for restaurant partners only. Please use the correct login portal for your account type.');
        return;
      }
      if (panelType === 'admin' && user.role !== 'admin') {
        setError('This portal is for administrators only. Please use the correct login portal for your account type.');
        return;
      }
      if (panelType === 'driver' && user.role !== 'driver' && user.role !== 'admin') {
        setError('This portal is for delivery drivers only. Please use the correct login portal for your account type.');
        return;
      }

      // Redirect based on role
      switch (user.role) {
        case 'admin':
          router.replace('/admin/dashboard');
          break;
        case 'vendor':
          router.replace('/vendor/dashboard');
          break;
        case 'driver':
          router.replace('/driver/dashboard');
          break;
        case 'customer':
        default:
          router.replace('/');
          break;
      }
    }
  }, [user, authLoading, router, panelType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await signIn(email, password);
      // The auth context and useEffect will handle the redirect based on user role
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign in';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setError('');
    setLoading(true);

    try {
      await signInWithGoogle();
      // The auth context and useEffect will handle the redirect based on user role
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign in with Google';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Don't render login form if user is already authenticated (will redirect)
  if (user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {user.role === 'admin' ? 'Redirecting to admin panel...' : 'Redirecting to dashboard...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="flex items-center space-x-2">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
              panelType === 'vendor' ? 'bg-orange-500' :
              panelType === 'admin' ? 'bg-blue-600' :
              panelType === 'driver' ? 'bg-green-600' :
              'bg-orange-500'
            }`}>
              {panelType === 'vendor' ? <Store className="w-6 h-6 text-white" /> :
               panelType === 'admin' ? <Shield className="w-6 h-6 text-white" /> :
               panelType === 'driver' ? <Truck className="w-6 h-6 text-white" /> :
               <span className="text-white font-bold text-xl">T</span>}
            </div>
            <div>
              <span className="text-2xl font-bold text-gray-900">Tap2Go</span>
              {panelType !== 'customer' && (
                <p className={`text-sm font-medium ${
                  panelType === 'vendor' ? 'text-orange-600' :
                  panelType === 'admin' ? 'text-blue-600' :
                  panelType === 'driver' ? 'text-green-600' :
                  'text-orange-600'
                }`}>
                  {panelType === 'vendor' ? 'Vendor Portal' :
                   panelType === 'admin' ? 'Admin Portal' :
                   panelType === 'driver' ? 'Driver Portal' : ''}
                </p>
              )}
            </div>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          {panelType === 'vendor' ? 'Restaurant Partner Sign In' :
           panelType === 'admin' ? 'Administrator Sign In' :
           panelType === 'driver' ? 'Driver Sign In' :
           'Sign in to your account'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          {panelType === 'vendor' ? 'Access your restaurant management dashboard' :
           panelType === 'admin' ? 'Access the platform management dashboard' :
           panelType === 'driver' ? 'Access your delivery dashboard and earnings' :
           <>Or{' '}
             <Link href="/auth/signup" className="font-medium hover:opacity-80" style={{ color: '#f3a823' }}>
               create a new account
             </Link>
           </>}
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="input-field"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="input-field pr-10"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <a href="#" className="font-medium text-orange-600 hover:text-orange-500">
                  Forgot your password?
                </a>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-full inline-flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                Continue with Google
              </button>

              <button
                type="button"
                disabled
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed"
              >
                <span className="sr-only">Sign in with Facebook</span>
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span className="ml-2 text-xs">Coming Soon</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
