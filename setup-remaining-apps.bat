@echo off
echo Setting up admin and driver apps...

REM Create admin app structure
mkdir apps\web-admin\src\app
robocopy "apps\web\src\app\admin" "apps\web-admin\src\app" /E
robocopy "apps\web\src\app\admin-login" "apps\web-admin\src\app\admin-login" /E
copy apps\web\src\app\globals.css apps\web-admin\src\app\globals.css
copy apps\web\src\app\not-found.tsx apps\web-admin\src\app\not-found.tsx
copy apps\web\src\app\error.tsx apps\web-admin\src\app\error.tsx
copy apps\web\src\app\favicon.ico apps\web-admin\src\app\favicon.ico

REM Create driver app structure  
mkdir apps\web-driver\src\app
robocopy "apps\web\src\app\driver" "apps\web-driver\src\app" /E
copy apps\web\src\app\globals.css apps\web-driver\src\app\globals.css
copy apps\web\src\app\not-found.tsx apps\web-driver\src\app\not-found.tsx
copy apps\web\src\app\error.tsx apps\web-driver\src\app\error.tsx
copy apps\web\src\app\favicon.ico apps\web-driver\src\app\favicon.ico

REM Copy shared libraries to all apps
robocopy "apps\web-customer\src\lib" "apps\web-admin\src\lib" /E
robocopy "apps\web-customer\src\lib" "apps\web-driver\src\lib" /E

REM Copy public directories
robocopy "apps\web\public" "apps\web-vendor\public" /E
robocopy "apps\web\public" "apps\web-admin\public" /E
robocopy "apps\web\public" "apps\web-driver\public" /E

echo All apps set up successfully!
